# Fair Value Gaps (FVG) Trading Strategy

## 📊 Opis strategii

Kompletna strategia handlowa w Pine Script 6 oparta na wykrywaniu i handlu lukach Fair Value Gaps. Strategia automatycznie identyfikuje luki cenowe, zarządza pozycjami z zaawansowanym systemem risk management i generuje alerty JSON dla automatyzacji.

## 🎯 Kluczowe funkcje

- ✅ **Automatyczne wykrywanie FVG** - bycze i niedźwiedzie luki
- ✅ **Zaawansowane filtrowanie** - ATR, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, minimalna wielkość
- ✅ **Zarządzanie ryzykiem** - SL/TP, maksymalna liczba pozycji
- ✅ **Alerty JSON** - integracja z botami handlowymi
- ✅ **Wizualizacja** - kolorowe pudełka, linie entry/SL/TP
- ✅ **Backtesting** - pełne wsparcie strategii TradingView
- ✅ **Multi-timeframe** - analiza na różnych interwałach

## 🚀 Szybki start

### 1. Instalacja w TradingView

1. Otwórz TradingView i przejdź do Pine Editor
2. Skopiuj kod z pliku `fvg_strategy.pine`
3. Wklej kod i zapisz jako "FVG Strategy"
4. Dodaj do wykresu

### 2. Podstawowa konfiguracja

```pine
// Zalecane ustawienia dla początkujących:
showFVG = true                    // Pokaż luki FVG
enableStrategy = true             // Włącz handel
fvgTimeframe = "1h"              // Timeframe dla FVG (wyższy niż wykres)
riskRewardRatio = 2.0            // Stosunek zysk/ryzyko
maxPositions = 1                 // Maksymalnie 1 pozycja jednocześnie
```

### 3. Ustawienia dla różnych stylów handlu

#### Scalping (5m chart)
```pine
fvgTimeframe = "15m"
riskRewardRatio = 1.5
maxAgeBars = 20
atrMultiplier = 0.3
```

#### Day Trading (15m chart)
```pine
fvgTimeframe = "1h"
riskRewardRatio = 2.0
maxAgeBars = 50
atrMultiplier = 0.5
```

#### Swing Trading (1h chart)
```pine
fvgTimeframe = "4h"
riskRewardRatio = 2.5
maxAgeBars = 100
atrMultiplier = 0.8
```

## 📋 Parametry strategii

### FVG Settings
- **Show Fair Value Gaps**: Włącz/wyłącz wizualizację luk
- **Enable Strategy Trading**: Włącz/wyłącz automatyczny handel
- **FVG Timeframe**: Timeframe do analizy luk (zalecane wyższy niż wykres)

### Filtering
- **Use Volatility Threshold**: Filtrowanie na podstawie zmienności
- **Min Gap Size (pips)**: Minimalna wielkość luki w pipsach
- **ATR Period**: Okres dla Average True Range (14)
- **ATR Multiplier**: Mnożnik ATR dla filtrowania (0.5)

### Risk Management
- **Risk/Reward Ratio**: Stosunek zysk/ryzyko (2.0)
- **Max Concurrent Positions**: Maksymalna liczba pozycji (3)
- **Max Gap Age (bars)**: Maksymalny wiek luki w świecach (50)

### Visual
- **Bullish/Bearish Gap Color**: Kolory luk
- **Box Extend (bars)**: Rozciągnięcie pudełek w prawo

## 🔍 Logika strategii

### Wykrywanie byczej luki (Bullish FVG)
```
Warunki:
1. Nowy timeframe (pierwsza świeca interwału)
2. current_low > previous_2_high (luka w górę)
3. previous_close > previous_2_high (potwierdzenie kierunku)
4. Spełnione filtry (zmienność, ATR, minimalna wielkość)
```

### Wykrywanie niedźwiedziej luki (Bearish FVG)
```
Warunki:
1. Nowy timeframe (pierwsza świeca interwału)
2. current_high < previous_2_low (luka w dół)
3. previous_close < previous_2_low (potwierdzenie kierunku)
4. Spełnione filtry (zmienność, ATR, minimalna wielkość)
```

### Poziomy transakcyjne
- **Entry**: Środek luki `(top + bottom) / 2`
- **Stop Loss**: Przeciwna strona luki
- **Take Profit**: Entry + (Entry - SL) × Risk/Reward Ratio

## 📊 Alerty i automatyzacja

### Format alertów JSON
```json
{
  "type": "BullishFVG",
  "symbol": "EURUSD",
  "entry": 1.0850,
  "sl": 1.0820,
  "tp": 1.0910,
  "time": "2025-06-14T10:30:00Z",
  "timeframe": "15m"
}
```

### Konfiguracja alertów
1. Kliknij prawym na wykres → "Add Alert"
2. Wybierz "FVG Strategy"
3. Ustaw warunki: "Bullish FVG" lub "Bearish FVG"
4. W polu wiadomości użyj JSON template
5. Skonfiguruj webhook URL dla automatyzacji

## 🎯 Optymalizacja parametrów

### Backtesting
1. Ustaw okres testowania (min. 6 miesięcy)
2. Włącz "Strategy Tester" w TradingView
3. Monitoruj kluczowe metryki:
   - Total Return
   - Sharpe Ratio
   - Maximum Drawdown
   - Win Rate
   - Profit Factor

### Parametry do optymalizacji
- `atrMultiplier`: 0.2 - 1.0 (krok 0.1)
- `riskRewardRatio`: 1.0 - 3.0 (krok 0.2)
- `minGapSize`: 0.0 - 10.0 (krok 0.5)
- `maxAgeBars`: 20 - 100 (krok 10)

## 🛠️ Rozwiązywanie problemów

### Brak wykrywanych luk
- Zmniejsz `minGapSize` i `atrMultiplier`
- Wyłącz `useVolatilityThreshold`
- Użyj wyższego timeframe dla FVG

### Zbyt dużo fałszywych sygnałów
- Zwiększ `minGapSize` i `atrMultiplier`
- Włącz `useVolatilityThreshold`
- Użyj wyższego timeframe

### Słaba wydajność
- Dostosuj `riskRewardRatio`
- Zmniejsz `maxAgeBars`
- Przetestuj różne timeframe
- Optymalizuj parametry na danych historycznych

## 📈 Zalecane rynki

### Forex
- Pary: EUR/USD, GBP/USD, USD/JPY, AUD/USD
- Timeframe: 15m chart, 1h FVG
- Risk/Reward: 1.5-2.0

### Kryptowaluty
- Symbole: BTC/USD, ETH/USD, ADA/USD, SOL/USD
- Timeframe: 1h chart, 4h FVG
- Risk/Reward: 2.0-2.5

### Akcje
- Symbole: SPY, QQQ, AAPL, TSLA
- Timeframe: 1h chart, 4h FVG
- Risk/Reward: 2.0

### Indeksy
- Symbole: SPX, NAS100, GER40, UK100
- Timeframe: 4h chart, 1D FVG
- Risk/Reward: 1.8-2.2

## 📝 Uwagi prawne

⚠️ **Ostrzeżenie o ryzyku**: Handel instrumentami finansowymi wiąże się z wysokim ryzykiem utraty kapitału. Strategia służy wyłącznie celom edukacyjnym. Zawsze testuj na koncie demo przed użyciem środków rzeczywistych.

## 🔄 Aktualizacje

- **v1.0.0** - Pierwsza wersja strategii Pine Script 6
- Planowane: Dodatkowe filtry, optymalizacja wydajności, więcej typów alertów

## 📞 Wsparcie

Jeśli masz pytania lub problemy:
1. Sprawdź sekcję "Rozwiązywanie problemów"
2. Przejrzyj plik `config.json` z przykładowymi ustawieniami
3. Przetestuj na różnych timeframe i instrumentach

---

**Powodzenia w handlu! 🚀**
