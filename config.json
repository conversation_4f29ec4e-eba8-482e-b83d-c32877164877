{"strategy_name": "Fair Value Gaps Strategy", "version": "1.0.0", "pine_script_version": 6, "description": "Kompletna strategia handlowa oparta na lukach Fair Value Gaps z zaawansowanym zarządzaniem ryzykiem", "default_parameters": {"fvg_settings": {"show_fvg": true, "enable_strategy": true, "fvg_timeframe": "", "description": "Główne ustawienia wykrywania FVG"}, "filtering": {"use_volatility_threshold": true, "min_gap_size_pips": 0.0, "atr_period": 14, "atr_multiplier": 0.5, "description": "Parametry filtrowania jakości luk FVG"}, "risk_management": {"risk_reward_ratio": 2.0, "max_concurrent_positions": 3, "max_gap_age_bars": 50, "initial_capital": 10000, "position_size_percent": 10, "description": "Zarządzanie ryzykiem i pozycjami"}, "visual_settings": {"bullish_gap_color": "rgba(0, 255, 0, 0.3)", "bearish_gap_color": "rgba(255, 0, 0, 0.3)", "box_extend_bars": 10, "show_info_table": true, "description": "Ustawienia wizualizacji na wykresie"}}, "recommended_timeframes": {"scalping": {"chart_timeframe": "5m", "fvg_timeframe": "15m", "description": "Szybkie transakcje intraday"}, "day_trading": {"chart_timeframe": "15m", "fvg_timeframe": "1h", "description": "Handel dzienny z średnim czasem utrzymania pozycji"}, "swing_trading": {"chart_timeframe": "1h", "fvg_timeframe": "4h", "description": "Handel pozycyjny na kilka dni"}, "position_trading": {"chart_timeframe": "4h", "fvg_timeframe": "1D", "description": "Długoterminowe pozycje"}}, "market_specific_settings": {"forex": {"min_gap_size_pips": 5.0, "atr_multiplier": 0.3, "risk_reward_ratio": 1.5, "recommended_pairs": ["EURUSD", "GBPUSD", "USDJPY", "AUDUSD"]}, "crypto": {"min_gap_size_pips": 0.0, "atr_multiplier": 0.8, "risk_reward_ratio": 2.5, "recommended_pairs": ["BTCUSD", "ETHUSD", "ADAUSD", "SOLUSD"]}, "stocks": {"min_gap_size_pips": 0.1, "atr_multiplier": 0.5, "risk_reward_ratio": 2.0, "recommended_symbols": ["SPY", "QQQ", "AAPL", "TSLA"]}, "indices": {"min_gap_size_pips": 1.0, "atr_multiplier": 0.4, "risk_reward_ratio": 1.8, "recommended_symbols": ["SPX", "NAS100", "GER40", "UK100"]}}, "alert_settings": {"webhook_format": "json", "include_fields": ["type", "symbol", "entry", "stop_loss", "take_profit", "timestamp", "timeframe"], "example_payload": {"type": "BullishFVG", "symbol": "EURUSD", "entry": 1.085, "sl": 1.082, "tp": 1.091, "time": "2025-06-14T10:30:00Z", "timeframe": "15m"}}, "backtesting_guidelines": {"minimum_data_period": "6 months", "recommended_data_period": "2 years", "key_metrics_to_monitor": ["Total Return", "<PERSON>", "Maximum Drawdown", "Win Rate", "Profit Factor", "Average Trade Duration"], "optimization_parameters": ["atr_multiplier", "risk_reward_ratio", "min_gap_size_pips", "max_gap_age_bars"]}, "performance_expectations": {"conservative": {"expected_win_rate": "45-55%", "expected_profit_factor": "1.2-1.5", "max_drawdown": "15-20%", "description": "Konserwatywne ustawienia z niższym ryzykiem"}, "moderate": {"expected_win_rate": "40-50%", "expected_profit_factor": "1.5-2.0", "max_drawdown": "20-30%", "description": "Zbalansowane ustawienia ryzyko/zysk"}, "aggressive": {"expected_win_rate": "35-45%", "expected_profit_factor": "2.0-3.0", "max_drawdown": "30-40%", "description": "Agresywne ustawienia z wyższym potencjałem zysku"}}, "troubleshooting": {"common_issues": {"no_gaps_detected": {"possible_causes": ["Z<PERSON>t wysoki próg min_gap_size_pips", "Zbyt wysoki atr_multiplier", "Nieodpowiedni timeframe FVG", "Niska zmienność rynku"], "solutions": ["<PERSON><PERSON><PERSON><PERSON><PERSON> min_gap_size_pips", "Zmniejsz atr_multiplier", "Użyj wyższego timeframe dla FVG", "Wyłącz use_volatility_threshold"]}, "too_many_false_signals": {"possible_causes": ["Zbyt niski próg filtrowania", "<PERSON><PERSON>t niski atr_multiplier", "Wysoka zmienność rynku"], "solutions": ["<PERSON><PERSON><PERSON><PERSON><PERSON> min_gap_size_pips", "Zwię<PERSON><PERSON> atr_multiplier", "Włącz use_volatility_threshold", "Użyj wyższego timeframe"]}, "poor_performance": {"possible_causes": ["Nieodpowiednie ustawienia risk/reward", "<PERSON><PERSON><PERSON> długi max_gap_age_bars", "Nieodpowiedni timeframe"], "solutions": ["Dostosuj risk_reward_ratio", "<PERSON><PERSON><PERSON><PERSON><PERSON> max_gap_age_bars", "Przetestuj różne timeframe", "Optymalizuj parametry na danych historycznych"]}}}, "integration": {"supported_platforms": ["TradingView", "MetaTrader (przez webhook)", "Custom trading bots", "Portfolio management systems"], "webhook_endpoints": {"example_url": "https://your-trading-bot.com/webhook/fvg-signals", "authentication": "API key required", "rate_limits": "Max 100 requests per minute"}}}