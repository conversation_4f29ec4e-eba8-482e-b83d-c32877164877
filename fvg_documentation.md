# Dokumentacja modułu Fair Value Gaps (FVG)

**Autor:** Doświadczony Full Stack Developer
**Język:** Pine Script v5
**Data:** 14 czerwca 2025

---

## Spis treści

- [Dokumentacja modułu Fair Value Gaps (FVG)](#dokumentacja-modułu-fair-value-gaps-fvg)
  - [Spis treści](#spis-treści)
  - [Wprowadzenie](#wprowadzenie)
  - [Cele modułu](#cele-modułu)
  - [Parametry i deklaracje input](#parametry-i-deklaracje-input)
  - [Stałe i typy danych](#stałe-i-typy-danych)
  - [Struktura `fairValueGap`](#struktura-fairvaluegap)
  - [Funkcje pomocnicze](#funkcje-pomocnicze)
    - [fairValueGapBox](#fairvaluegapbox)
    - [deleteFairValueGaps](#deletefairvaluegaps)
  - [Główna logika: `drawFairValueGaps`](#główna-logika-drawfairvaluegaps)
    - [7.1. Pobieranie danych z innego interwału](#71-pobieranie-danych-z-innego-interwału)
    - [7.2. Obliczanie progu filtrowania (`threshold`)](#72-obliczanie-progu-filtrowania-threshold)
    - [7.3. Flaga nowego interwału (`newTimeframe`)](#73-flaga-nowego-interwału-newtimeframe)
    - [7.4. Warunki detekcji luk](#74-warunki-detekcji-luk)
    - [7.5. Tworzenie obiektów wizualnych](#75-tworzenie-obiektów-wizualnych)
    - [7.6. Generowanie alertów](#76-generowanie-alertów)
  - [Zarządzanie cyklem życia luk](#zarządzanie-cyklem-życia-luk)
  - [Alerty i `alertcondition`](#alerty-i-alertcondition)
  - [Problemy, ograniczenia i uwagi](#problemy-ograniczenia-i-uwagi)
  - [Możliwości optymalizacji i usprawnienia](#możliwości-optymalizacji-i-usprawnienia)
  - [Przykładowe fragmenty kodu](#przykładowe-fragmenty-kodu)
  - [Podsumowanie](#podsumowanie)

---

## Wprowadzenie

Moduł FVG w Pine Script v5 służy do automatycznego wykrywania i wizualizacji luk cenowych typu Fair Value Gap, definiowanych jako przerwa między ekstremami świec w sekwencji trzech świec.

## Cele modułu

- Identyfikacja byczych i niedźwiedzich luk FVG.
- Dynamiczne filtrowanie luk na podstawie zmienności procentowej i ATR (opcjonalnie).
- Wizualizacja obszaru luk: prostokąty (`box`) i linie: Entry (`centerLine`), Stop-Loss (`redLine`) i Take-Profit (`greenLine`).
- Automatyczne usuwanie luk po ich zrealizowaniu (przebiciu) lub po określonej liczbie świec.
- Wysyłanie alertów z pełnym JSON-em sygnału (typ, Entry, SL, TP, czas, interwał).

## Parametry i deklaracje input

```pine
//@version=5
indicator("Fair Value Gaps", overlay=true)

// Włączenie modułu
showFairValueGapsInput    = input.bool(false, title="Show Fair Value Gaps")
// Próg na podstawie zmienności procentowej
fairValueGapsThresholdInput = input.bool(true, title="Use Volatility Threshold")
// Dodatkowy minimalny rozmiar gapu (pips)
minGapSizeInput           = input.float(0.0, title="Min Gap Size (pips)", step=0.1)
// Interwał do analizy
fairValueGapsTimeframeInput = input.timeframe("", title="FVG Timeframe (e.g. 15, 60, D)")
// Kolory luk
fairValueGapsBullColorInput = input.color(color.new(color.lime, 30), title="Bull Gap Color")
fairValueGapsBearColorInput = input.color(color.new(color.red, 30), title="Bear Gap Color")
// Rozciągnięcie pudełek
fairValueGapsExtendInput   = input.int(1, title="Box Extend (bars)")
// ATR dla dodatkowego filtrowania
atrPeriodInput             = input.int(14, title="ATR Period for Gap Filter")
atrMultiplierInput         = input.float(0.5, title="ATR Multiplier for Gap Filter")
```

- `minGapSizeInput` pozwala wykluczyć minimalne szczeliny.
- ATR można użyć do dodatkowego filtra: `gapSize > atr * atrMultiplierInput`.

## Stałe i typy danych

```pine
// Bias
BULLISH = 1
BEARISH = -1

type fairValueGap
    float top
    float bottom
    int   bias
    int   creationBarIndex
    box   topBox
    box   bottomBox
    line  centerLine
    line  redLine
    line  greenLine

var array<fairValueGap> fairValueGaps = array.new<fairValueGap>()
```

- `creationBarIndex` – indeks słupka przy tworzeniu, do wygaśnięcia czasowego.

## Struktura `fairValueGap`

- **top** – górna granica luki (dla byczej gap: `last2High`, dla niedźwiedziej gap: `currentHigh`).
- **bottom** – dolna granica luki (dla byczej gap: `currentLow`, dla niedźwiedziej gap: `last2Low`).
- **bias** – kierunek (`BULLISH`/`BEARISH`).
- **creationBarIndex** – `bar_index` utworzenia.
- **topBox**, **bottomBox** – obiekty `box` wizualizujące gap.
- **centerLine**, **redLine**, **greenLine** – obiekty `line` dla wejścia, SL, TP.

## Funkcje pomocnicze

### fairValueGapBox

```pine
fairValueGapBox(_leftTime, _rightTime, _topPrice, _bottomPrice, _boxColor) =>
    box.new(
        chart.point.new(_leftTime, na, _topPrice),
        chart.point.new(_rightTime + fairValueGapsExtendInput * (time - time[1]), na, _bottomPrice),
        xloc = xloc.bar_time,
        border_color = _boxColor,
        bgcolor = _boxColor
    )
```

- Tworzy `box` pomiędzy dwiema datami i cenami.

### deleteFairValueGaps

```pine
deleteFairValueGaps(_maxAgeBars) =>
    for idx = array.size(fairValueGaps) - 1 to 0
        gap = array.get(fairValueGaps, idx)
        // Usuwanie przy przebiciu
        if (gap.bias == BULLISH and low < gap.bottom) or (gap.bias == BEARISH and high > gap.top)
            // lub wygasanie czasowe
            or (bar_index - gap.creationBarIndex > _maxAgeBars)
            gap.topBox.delete()
            gap.bottomBox.delete()
            gap.centerLine.delete()
            gap.redLine.delete()
            gap.greenLine.delete()
            array.remove(fairValueGaps, idx)
```

- Dodano parametr `_maxAgeBars` do wygasania po określonym czasie.

## Główna logika: `drawFairValueGaps`

### 7.1. Pobieranie danych z innego interwału

```pine
[lastClose, lastOpen, lastTime,
 currentHigh, currentLow, currentTime,
 last2High, last2Low] = request.security(
    syminfo.tickerid,
    fairValueGapsTimeframeInput,
    [close[1], open[1], time[1],
     high[0], low[0], time[0],
     high[2], low[2]],
    lookahead = barmerge.lookahead_on
)
```

- Uwaga: `lookahead_on` może powodować repaint.
- Alternatywnie użyć `barmerge.lookahead_off` z `barstate.isconfirmed`.

### 7.2. Obliczanie progu filtrowania (`threshold`)

```pine
barDeltaPercent = (lastClose - lastOpen) / (lastOpen) * 100
newTimeframe    = timeframe.change(fairValueGapsTimeframeInput)
threshold       = fairValueGapsThresholdInput
    ? ta.cum(math.abs(newTimeframe ? barDeltaPercent : 0)) / bar_index * 2
    : 0
```

- `threshold` resetowany przy `newTimeframe`.
- Umożliwia dynamiczne filtrowanie na bazie historycznej zmienności.

### 7.3. Flaga nowego interwału (`newTimeframe`)

- `true` tylko na pierwszej świecy nowego interwału.
- Zapobiega liczeniu `threshold` na każdej świecy.

### 7.4. Warunki detekcji luk

**Bycza luka**:

```pine
isBullGap = newTimeframe and
    currentLow > last2High and
    lastClose > last2High and
    barDeltaPercent > threshold and
    ( (currentLow - last2High) >= minGapSizeInput * syminfo.mintick )
```

**Niedźwiedzia luka**:

```pine
isBearGap = newTimeframe and
    currentHigh < last2Low and
    lastClose < last2Low and
    -barDeltaPercent > threshold and
    ( (last2Low - currentHigh) >= minGapSizeInput * syminfo.mintick )
```

- Dodatkowy warunek: `minGapSizeInput` w tickach.

### 7.5. Tworzenie obiektów wizualnych

Po wykryciu luki:

1. **centerPrice** = `(extremum1 + extremum2) / 2`
2. Rysowanie:

   - `topBox` i `bottomBox` z kolorami `fairValueGapsBullColorInput`/`fairValueGapsBearColorInput`.
   - `centerLine` – biały: wejście.
   - `redLine` – czerwona SL na cenie ekstremum przeciwnego.
   - `greenLine` – zielona TP symetryczne.

3. Dodanie do tablicy:

```pine
gap = fairValueGap.new(
    top=...,
    bottom=...,
    bias=isBullGap ? BULLISH : BEARISH,
    creationBarIndex=bar_index,
    topBox=...,
    bottomBox=...,
    centerLine=...,
    redLine=...,
    greenLine=...
)
array.unshift(fairValueGaps, gap)
```

### 7.6. Generowanie alertów

- JSON sygnału:

```json
{
  "type": "BullishFVG",
  "symbol": "{{ticker}}",
  "entry": "{{centerPrice}}",
  "sl": "{{stopLoss}}",
  "tp": "{{takeProfit}}",
  "time": "{{timenow}}",
  "timeframe": "{{interval}}"
}
```

- Wysyłane przez `alert()` wewnątrz `drawFairValueGaps()`.

## Zarządzanie cyklem życia luk

```pine
if showFairValueGapsInput
    deleteFairValueGaps(maxAgeBars = 50)
    drawFairValueGaps()
```

- `deleteFairValueGaps` usuwa zrealizowane i przeterminowane luki.
- `drawFairValueGaps` wykrywa i rysuje nowe.

## Alerty i `alertcondition`

```pine
alertcondition(isBullGap, "Bullish FVG", "Bullish Fair Value Gap formed")
alertcondition(isBearGap, "Bearish FVG", "Bearish Fair Value Gap formed")
```

- Pozwala na użycie standardowych alertów w TradingView.

## Problemy, ograniczenia i uwagi

- **Repainting** – `lookahead_on`.
- **Sztywność** – tylko formacja 3-świecowa w początku interwału.
- **Parametryzacja** – dodano `minGapSizeInput` i ATR.
- **Czas życia** – luki wygasają po `maxAgeBars`.
- **UI** – optymalizacja liczby obiektów graficznych.

## Możliwości optymalizacji i usprawnienia

1. **Repaint off**: `lookahead_off`, `barstate.isconfirmed`.
2. **Dynamiczne sekwencje**: pętla `for i in 2..0`.
3. **Filtr ATR**: `atr(atrPeriodInput) * atrMultiplierInput`.
4. **Różne kolory**: odróżnienie incomplete vs complete.
5. **Limit zasobów**: zmniejszyć ilość obiektów per gap.
6. **Eksport JSON**: bardziej rozbudowana struktura.

## Przykładowe fragmenty kodu

```pine
// Przykład detekcji
if isBullGap
    // rysuj gap
```

```pine
// Usuwanie starych gapów
deleteFairValueGaps(maxAgeBars=50)
```

## Podsumowanie

Dokumentacja zawiera wszystkie kluczowe elementy logiki detekcji, filtrowania, wizualizacji oraz zarządzania Fair Value Gaps w Pine Script v5.
Szczegółowo opisano strukturę danych, funkcje pomocnicze, warunki detekcji, generowanie alertów oraz propozycje usprawnień.
