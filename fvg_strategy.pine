//@version=6
strategy("Fair Value Gaps Strategy", overlay=true, initial_capital=10000, default_qty_type=strategy.percent_of_equity, default_qty_value=10)

// ============================================================================
// PARAMETRY WEJŚCIOWE
// ============================================================================

// Główne ustawienia strategii
showFVG = input.bool(true, title="Show Fair Value Gaps", group="FVG Settings")
enableStrategy = input.bool(true, title="Enable Strategy Trading", group="FVG Settings")
fvgTimeframe = input.timeframe("", title="FVG Timeframe", group="FVG Settings")

// Filtrowanie luk
useVolatilityThreshold = input.bool(true, title="Use Volatility Threshold", group="Filtering")
minGapSize = input.float(0.0, title="Min Gap Size (pips)", step=0.1, group="Filtering")
atrPeriod = input.int(14, title="ATR Period", minval=1, group="Filtering")
atrMultiplier = input.float(0.5, title="ATR Multiplier", step=0.1, group="Filtering")

// Zarządzanie pozycjami
riskRewardRatio = input.float(2.0, title="Risk/Reward Ratio", step=0.1, group="Risk Management")
maxPositions = input.int(3, title="Max Concurrent Positions", minval=1, group="Risk Management")
maxAgeBars = input.int(50, title="Max Gap Age (bars)", minval=1, group="Risk Management")

// Wizualizacja
bullColor = input.color(color.new(color.lime, 70), title="Bullish Gap Color", group="Visual")
bearColor = input.color(color.new(color.red, 70), title="Bearish Gap Color", group="Visual")
boxExtend = input.int(10, title="Box Extend (bars)", minval=1, group="Visual")

// ============================================================================
// STAŁE I TYPY DANYCH
// ============================================================================

// Bias kierunkowy
BULLISH = 1
BEARISH = -1

// Struktura Fair Value Gap
type FairValueGap
    float top
    float bottom
    int bias
    int creationBarIndex
    float entryPrice
    float stopLoss
    float takeProfit
    box gapBox
    line entryLine
    line slLine
    line tpLine
    bool isActive

// Globalne zmienne
var array<FairValueGap> activeFVGs = array.new<FairValueGap>()
var int positionCount = 0

// ============================================================================
// FUNKCJE POMOCNICZE
// ============================================================================

// Tworzenie pudełka FVG
createFVGBox(leftBar, rightBar, topPrice, bottomPrice, boxColor) =>
    box.new(
        left=leftBar,
        top=topPrice,
        right=rightBar + boxExtend,
        bottom=bottomPrice,
        border_color=boxColor,
        bgcolor=boxColor,
        extend=extend.none
    )

// Tworzenie linii
createLine(x1, y1, x2, y2, lineColor, lineStyle=line.style_solid) =>
    line.new(
        x1=x1, y1=y1,
        x2=x2, y2=y2,
        color=lineColor,
        style=lineStyle,
        width=1
    )

// Obliczanie poziomu entry, SL i TP
calculateLevels(gapTop, gapBottom, bias) =>
    entryPrice = (gapTop + gapBottom) / 2

    if bias == BULLISH
        stopLoss = gapBottom
        takeProfit = entryPrice + (entryPrice - stopLoss) * riskRewardRatio
    else
        stopLoss = gapTop
        takeProfit = entryPrice - (stopLoss - entryPrice) * riskRewardRatio

    [entryPrice, stopLoss, takeProfit]

// Usuwanie nieaktywnych FVG
cleanupFVGs() =>
    if array.size(activeFVGs) > 0
        for i = array.size(activeFVGs) - 1 to 0
            fvg = array.get(activeFVGs, i)
            shouldRemove = false

            // Sprawdzenie czy gap został wypełniony
            if fvg.bias == BULLISH and low <= fvg.bottom
                shouldRemove := true
            else if fvg.bias == BEARISH and high >= fvg.top
                shouldRemove := true

            // Sprawdzenie wieku
            if bar_index - fvg.creationBarIndex > maxAgeBars
                shouldRemove := true

            // Usuwanie obiektów graficznych i z tablicy
            if shouldRemove
                box.delete(fvg.gapBox)
                line.delete(fvg.entryLine)
                line.delete(fvg.slLine)
                line.delete(fvg.tpLine)
                array.remove(activeFVGs, i)

// ============================================================================
// GŁÓWNA LOGIKA DETEKCJI FVG
// ============================================================================

detectFVG() =>
    // Pobieranie danych z wybranego timeframe
    securityData = request.security(syminfo.tickerid, fvgTimeframe,
        [close[1], open[1], time[1], high[0], low[0], time[0], high[2], low[2]],
        lookahead=barmerge.lookahead_off)

    [prevClose, prevOpen, prevTime, currHigh, currLow, currTime, prev2High, prev2Low] = securityData

    // Sprawdzenie nowego timeframe
    newTF = timeframe.change(fvgTimeframe)

    if not newTF
        [false, false, na, na, na]
    else
        // Obliczanie zmienności
        barDeltaPercent = math.abs((prevClose - prevOpen) / prevOpen * 100)
        atrValue = ta.atr(atrPeriod)

        // Próg zmienności
        threshold = useVolatilityThreshold ? ta.sma(barDeltaPercent, 20) * 0.5 : 0

        // Warunki dla byczej luki
        isBullGap = currLow > prev2High and
                   prevClose > prev2High and
                   barDeltaPercent > threshold and
                   (currLow - prev2High) >= minGapSize * syminfo.mintick and
                   (currLow - prev2High) >= atrValue * atrMultiplier

        // Warunki dla niedźwiedziej luki
        isBearGap = currHigh < prev2Low and
                   prevClose < prev2Low and
                   barDeltaPercent > threshold and
                   (prev2Low - currHigh) >= minGapSize * syminfo.mintick and
                   (prev2Low - currHigh) >= atrValue * atrMultiplier

        gapTop = isBullGap ? currLow : (isBearGap ? prev2Low : na)
        gapBottom = isBullGap ? prev2High : (isBearGap ? currHigh : na)
        bias = isBullGap ? BULLISH : (isBearGap ? BEARISH : na)

        [isBullGap, isBearGap, gapTop, gapBottom, bias]

// ============================================================================
// TWORZENIE FVG
// ============================================================================

createFVG(gapTop, gapBottom, bias) =>
    [entryPrice, stopLoss, takeProfit] = calculateLevels(gapTop, gapBottom, bias)

    // Kolory
    gapColor = bias == BULLISH ? bullColor : bearColor

    // Tworzenie obiektów graficznych
    gapBox = createFVGBox(bar_index, bar_index, gapTop, gapBottom, gapColor)
    entryLine = createLine(bar_index, entryPrice, bar_index + boxExtend, entryPrice, color.white)
    slLine = createLine(bar_index, stopLoss, bar_index + boxExtend, stopLoss, color.red)
    tpLine = createLine(bar_index, takeProfit, bar_index + boxExtend, takeProfit, color.green)

    // Tworzenie struktury FVG
    newFVG = FairValueGap.new(
        top=gapTop,
        bottom=gapBottom,
        bias=bias,
        creationBarIndex=bar_index,
        entryPrice=entryPrice,
        stopLoss=stopLoss,
        takeProfit=takeProfit,
        gapBox=gapBox,
        entryLine=entryLine,
        slLine=slLine,
        tpLine=tpLine,
        isActive=true
    )

    array.push(activeFVGs, newFVG)

    // Alert JSON
    alertType = bias == BULLISH ? 'BullishFVG' : 'BearishFVG'
    alertMessage = '{"type":"' + alertType + '","symbol":"' + syminfo.ticker +
                  '","entry":' + str.tostring(entryPrice) + ',"sl":' + str.tostring(stopLoss) +
                  ',"tp":' + str.tostring(takeProfit) + ',"time":"' + str.tostring(time) +
                  '","timeframe":"' + fvgTimeframe + '"}'

    alert(alertMessage, alert.freq_once_per_bar)

    newFVG

// ============================================================================
// LOGIKA STRATEGII
// ============================================================================

// Główna logika
if showFVG
    cleanupFVGs()

    [isBullGap, isBearGap, gapTop, gapBottom, bias] = detectFVG()

    if (isBullGap or isBearGap) and not na(bias)
        newFVG = createFVG(gapTop, gapBottom, bias)

        // Wykonanie transakcji jeśli strategia jest włączona
        if enableStrategy and strategy.position_size == 0 and positionCount < maxPositions
            if bias == BULLISH
                strategy.entry("Long FVG", strategy.long, stop=newFVG.entryPrice)
                strategy.exit("Exit Long", "Long FVG", stop=newFVG.stopLoss, limit=newFVG.takeProfit)
            else if bias == BEARISH
                strategy.entry("Short FVG", strategy.short, stop=newFVG.entryPrice)
                strategy.exit("Exit Short", "Short FVG", stop=newFVG.stopLoss, limit=newFVG.takeProfit)

            positionCount := positionCount + 1

// Reset licznika pozycji gdy pozycja jest zamknięta
if strategy.position_size == 0
    positionCount := 0

// ============================================================================
// ALERTY
// ============================================================================

[isBullGap, isBearGap, _, _, _] = detectFVG()

alertcondition(isBullGap, "Bullish FVG", "Bullish Fair Value Gap Detected")
alertcondition(isBearGap, "Bearish FVG", "Bearish Fair Value Gap Detected")

// ============================================================================
// WYŚWIETLANIE INFORMACJI
// ============================================================================

// Tabela z informacjami o strategii
if barstate.islast and showFVG
    var table infoTable = table.new(position.top_right, 2, 4, bgcolor=color.white, border_width=1)
    table.cell(infoTable, 0, 0, "Active FVGs:", text_color=color.black)
    table.cell(infoTable, 1, 0, str.tostring(array.size(activeFVGs)), text_color=color.blue)
    table.cell(infoTable, 0, 1, "Position Size:", text_color=color.black)
    table.cell(infoTable, 1, 1, str.tostring(strategy.position_size), text_color=color.blue)
    table.cell(infoTable, 0, 2, "Total Trades:", text_color=color.black)
    table.cell(infoTable, 1, 2, str.tostring(strategy.closedtrades), text_color=color.blue)
    table.cell(infoTable, 0, 3, "Win Rate:", text_color=color.black)
    winRate = strategy.closedtrades > 0 ? strategy.wintrades / strategy.closedtrades * 100 : 0
    table.cell(infoTable, 1, 3, str.tostring(winRate, "#.##") + "%", text_color=color.green)
