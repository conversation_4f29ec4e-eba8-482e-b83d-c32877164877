//@version=6
strategy("FVG Strategy Simple", overlay=true, initial_capital=10000, default_qty_type=strategy.percent_of_equity, default_qty_value=10)

// ============================================================================
// PARAMETRY WEJŚCIOWE
// ============================================================================

// Główne ustawienia
showFVG = input.bool(true, title="Show Fair Value Gaps", group="FVG Settings")
enableStrategy = input.bool(true, title="Enable Strategy Trading", group="FVG Settings")
fvgTimeframe = input.timeframe("", title="FVG Timeframe", group="FVG Settings")

// Filtrowanie
minGapSize = input.float(0.0, title="Min Gap Size (pips)", step=0.1, group="Filtering")
atrPeriod = input.int(14, title="ATR Period", minval=1, group="Filtering")
atrMultiplier = input.float(0.5, title="ATR Multiplier", step=0.1, group="Filtering")

// Risk Management
riskRewardRatio = input.float(2.0, title="Risk/Reward Ratio", step=0.1, group="Risk Management")
maxAgeBars = input.int(50, title="Max Gap Age (bars)", minval=1, group="Risk Management")

// Kolory
bullColor = input.color(color.new(color.lime, 70), title="Bullish Gap Color", group="Visual")
bearColor = input.color(color.new(color.red, 70), title="Bearish Gap Color", group="Visual")

// ============================================================================
// STAŁE
// ============================================================================

BULLISH = 1
BEARISH = -1

// ============================================================================
// ZMIENNE GLOBALNE
// ============================================================================

var array<box> fvgBoxes = array.new<box>()
var array<line> fvgLines = array.new<line>()
var array<int> fvgBias = array.new<int>()
var array<int> fvgAge = array.new<int>()

// ============================================================================
// FUNKCJE POMOCNICZE
// ============================================================================

// Czyszczenie starych FVG
cleanupOldFVG() =>
    if array.size(fvgBoxes) > 0
        for i = array.size(fvgBoxes) - 1 to 0
            currentAge = array.get(fvgAge, i)
            currentBias = array.get(fvgBias, i)
            
            shouldRemove = false
            
            // Sprawdzenie wieku
            if bar_index - currentAge > maxAgeBars
                shouldRemove := true
            
            // Sprawdzenie wypełnienia
            if currentBias == BULLISH and low <= low[1]
                shouldRemove := true
            else if currentBias == BEARISH and high >= high[1]
                shouldRemove := true
            
            if shouldRemove
                box.delete(array.get(fvgBoxes, i))
                line.delete(array.get(fvgLines, i))
                array.remove(fvgBoxes, i)
                array.remove(fvgLines, i)
                array.remove(fvgBias, i)
                array.remove(fvgAge, i)

// ============================================================================
// GŁÓWNA LOGIKA DETEKCJI FVG
// ============================================================================

// Pobieranie danych z wyższego timeframe
[htfHigh, htfLow, htfClose, htfHigh1, htfLow1, htfHigh2, htfLow2] = request.security(
    syminfo.tickerid, 
    fvgTimeframe,
    [high, low, close, high[1], low[1], high[2], low[2]],
    lookahead=barmerge.lookahead_off
)

// Sprawdzenie nowego timeframe
newTF = timeframe.change(fvgTimeframe)

// Detekcja FVG
if newTF and showFVG
    cleanupOldFVG()
    
    // ATR dla filtrowania
    atrValue = ta.atr(atrPeriod)
    
    // Warunki dla byczej luki
    isBullGap = htfLow > htfHigh2 and 
               htfClose[1] > htfHigh2 and
               (htfLow - htfHigh2) >= minGapSize * syminfo.mintick and
               (htfLow - htfHigh2) >= atrValue * atrMultiplier
    
    // Warunki dla niedźwiedziej luki  
    isBearGap = htfHigh < htfLow2 and 
               htfClose[1] < htfLow2 and
               (htfLow2 - htfHigh) >= minGapSize * syminfo.mintick and
               (htfLow2 - htfHigh) >= atrValue * atrMultiplier
    
    // Tworzenie byczej luki
    if isBullGap
        gapTop = htfLow
        gapBottom = htfHigh2
        entryPrice = (gapTop + gapBottom) / 2
        stopLoss = gapBottom
        takeProfit = entryPrice + (entryPrice - stopLoss) * riskRewardRatio
        
        // Rysowanie pudełka
        fvgBox = box.new(
            left=bar_index,
            top=gapTop,
            right=bar_index + 10,
            bottom=gapBottom,
            border_color=bullColor,
            bgcolor=bullColor
        )
        
        // Linia entry
        entryLine = line.new(
            x1=bar_index,
            y1=entryPrice,
            x2=bar_index + 10,
            y2=entryPrice,
            color=color.white,
            width=2
        )
        
        // Dodanie do tablic
        array.push(fvgBoxes, fvgBox)
        array.push(fvgLines, entryLine)
        array.push(fvgBias, BULLISH)
        array.push(fvgAge, bar_index)
        
        // Wykonanie transakcji
        if enableStrategy and strategy.position_size == 0
            strategy.entry("Long FVG", strategy.long, stop=entryPrice)
            strategy.exit("Exit Long", "Long FVG", stop=stopLoss, limit=takeProfit)
        
        // Alert
        alertMessage = '{"type":"BullishFVG","symbol":"' + syminfo.ticker + 
                      '","entry":' + str.tostring(entryPrice) + 
                      ',"sl":' + str.tostring(stopLoss) + 
                      ',"tp":' + str.tostring(takeProfit) + '"}'
        alert(alertMessage, alert.freq_once_per_bar)
    
    // Tworzenie niedźwiedziej luki
    if isBearGap
        gapTop = htfLow2
        gapBottom = htfHigh
        entryPrice = (gapTop + gapBottom) / 2
        stopLoss = gapTop
        takeProfit = entryPrice - (stopLoss - entryPrice) * riskRewardRatio
        
        // Rysowanie pudełka
        fvgBox = box.new(
            left=bar_index,
            top=gapTop,
            right=bar_index + 10,
            bottom=gapBottom,
            border_color=bearColor,
            bgcolor=bearColor
        )
        
        // Linia entry
        entryLine = line.new(
            x1=bar_index,
            y1=entryPrice,
            x2=bar_index + 10,
            y2=entryPrice,
            color=color.white,
            width=2
        )
        
        // Dodanie do tablic
        array.push(fvgBoxes, fvgBox)
        array.push(fvgLines, entryLine)
        array.push(fvgBias, BEARISH)
        array.push(fvgAge, bar_index)
        
        // Wykonanie transakcji
        if enableStrategy and strategy.position_size == 0
            strategy.entry("Short FVG", strategy.short, stop=entryPrice)
            strategy.exit("Exit Short", "Short FVG", stop=stopLoss, limit=takeProfit)
        
        // Alert
        alertMessage = '{"type":"BearishFVG","symbol":"' + syminfo.ticker + 
                      '","entry":' + str.tostring(entryPrice) + 
                      ',"sl":' + str.tostring(stopLoss) + 
                      ',"tp":' + str.tostring(takeProfit) + '"}'
        alert(alertMessage, alert.freq_once_per_bar)

// ============================================================================
// ALERTY
// ============================================================================

// Ponowne sprawdzenie dla alertcondition
[htfHigh_alert, htfLow_alert, htfClose_alert, htfHigh1_alert, htfLow1_alert, htfHigh2_alert, htfLow2_alert] = request.security(
    syminfo.tickerid, 
    fvgTimeframe,
    [high, low, close, high[1], low[1], high[2], low[2]],
    lookahead=barmerge.lookahead_off
)

newTF_alert = timeframe.change(fvgTimeframe)
atrValue_alert = ta.atr(atrPeriod)

isBullGap_alert = newTF_alert and htfLow_alert > htfHigh2_alert and 
                 htfClose_alert[1] > htfHigh2_alert and
                 (htfLow_alert - htfHigh2_alert) >= atrValue_alert * atrMultiplier

isBearGap_alert = newTF_alert and htfHigh_alert < htfLow2_alert and 
                 htfClose_alert[1] < htfLow2_alert and
                 (htfLow2_alert - htfHigh_alert) >= atrValue_alert * atrMultiplier

alertcondition(isBullGap_alert, "Bullish FVG", "Bullish Fair Value Gap Detected")
alertcondition(isBearGap_alert, "Bearish FVG", "Bearish Fair Value Gap Detected")

// ============================================================================
// INFORMACJE
// ============================================================================

// Tabela informacyjna
if barstate.islast and showFVG
    var table infoTable = table.new(position.top_right, 2, 3, bgcolor=color.white, border_width=1)
    table.cell(infoTable, 0, 0, "Active FVGs:", text_color=color.black)
    table.cell(infoTable, 1, 0, str.tostring(array.size(fvgBoxes)), text_color=color.blue)
    table.cell(infoTable, 0, 1, "Position Size:", text_color=color.black)
    table.cell(infoTable, 1, 1, str.tostring(strategy.position_size), text_color=color.blue)
    table.cell(infoTable, 0, 2, "Total Trades:", text_color=color.black)
    table.cell(infoTable, 1, 2, str.tostring(strategy.closedtrades), text_color=color.blue)
